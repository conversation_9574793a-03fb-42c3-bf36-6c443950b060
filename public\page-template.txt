<div>
  <div v-if="showAuditData && showAuditData.length > 0 && showAuditData[0].contract">
    <div>
      <div style="font-weight: bold;">{{showAuditData[0].contract.supplierName}}</div>
      <div>{{showAuditData[0].contract.customerName}}</div>
      <div style="display: flex; justify-content: space-between;">
        <div>合同编号：{{showAuditData[0].contract.no}}</div>
        <div>原客户订单号：{{showAuditData[0].contract.customerPo}}</div>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <div>下单时间：{{showAuditData[0].contract.orderDate}}</div>
        <div style="color: #007bff; font-weight: bold;">业务员：{{showAuditData[0].contract.userName}}</div>
      </div>
      <div style="word-break: break-all;">
        <div>送货地址：{{showAuditData[0].contract.deliveryPlace}}</div>
      </div>
      <div style="display: flex;">
        客户需求：
        <div>{{showAuditData[0].contract.freightWay}}</div>
        <div style="padding-left: 8px;">{{showAuditData[0].contract.deliveryWay}}</div>
        <div style="padding-left: 8px;">{{showAuditData[0].contract.taxDescript}}</div>
        <div style="padding-left: 8px;">{{showAuditData[0].contract.payWay}}</div>
      </div>
      <div style="color: #007bff; font-weight: bold;">
        总金额：{{showAuditData[0].contract.currencyType}}{{showAuditData[0].contract.totalAmt}}</div>
      <div>总面积：{{showAuditData[0].contract.area}}</div>
    </div>
    <div>
      <div style="display: flex; justify-content: space-between; align-items: center; padding-bottom: 4px; border-bottom: 1px solid #dee2e6;">
        <div style="font-weight: bold;">订单明细</div>
        <button id="toggle-button" style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">
          {{flag == '1' ? '收起' : '展开'}}
        </button>
      </div>
      <div id="detail-section" v-if="flag == '1'">
        <template v-for="item in showAuditData">
          <div style="padding: 4px 0; border-bottom: 1px solid #dee2e6; background-color: #f8f9fa;" :key="item.recordId">
            <div style="word-break: break-all">客户型号：{{item.customerModel}}</div>
            <div>
              <span>数量：{{item.quantity}}</span>
              <span style="padding-left: 8px;">面积：{{item.deailArea}}</span>
              <span style="padding-left: 8px;">单价：{{item.price}}</span>
              <span style="padding-left: 8px;">金额：{{item.totalAmt}}</span>
              <span style="padding-left: 8px;">业务费：{{item.saleFee}}</span>
              <span style="padding-left: 8px;">管理费：{{item.manageFee}}</span>
            </div>
            <div style="display: flex; justify-content: space-between;">
              <div>PCS：{{item.unitLength}} * {{item.unitWidth}}</div>
              <div>PNL：{{item.pnlLength}} * {{item.pnlWidth}} / {{item.pnlDivisor}}</div>
            </div>
            <div style="word-break: break-all;">
              <span v-if="item.boardLevel">{{item.boardLevel}}</span>
              <span v-if="item.materialType" style="padding-left: 8px;">{{item.materialType}}</span>
              <span v-if="item.boardThickness" style="padding-left: 8px;">{{item.boardThickness}}</span>
              <span v-if="item.copperCladThickness" style="padding-left: 8px;">{{item.copperCladThickness}}</span>
              <span v-if="item.surfaceProcess" style="padding-left: 8px;">{{item.surfaceProcess}}</span>
              <span v-if="item.solderMaskType" style="padding-left: 8px;">{{item.solderMaskType}}</span>
              <span v-if="item.characterType" style="padding-left: 8px;">{{item.characterType}}</span>
              <span v-if="item.shapingWay" style="padding-left: 8px;">{{item.shapingWay}}</span>
              <span v-if="item.testMethod" style="padding-left: 8px;">{{item.testMethod}}</span>
              <span v-if="item.smallAperture" style="padding-left: 8px;">{{item.smallAperture}}</span>
              <span v-if="item.buryBlindHole" style="padding-left: 8px;">{{item.buryBlindHole}}</span>
              <span v-if="item.deliveryUrgent" style="padding-left: 8px;">{{item.deliveryUrgent}}</span>
              <span v-if="item.daore" style="padding-left: 8px;">{{item.daore}}</span>
              <span v-if="item.naiya" style="padding-left: 8px;">{{item.naiya}}</span>
              <span v-if="item.lingeSpacing" style="padding-left: 8px;">{{item.lingeSpacing}}</span>
              <span v-if="item.halAhole" style="padding-left: 8px;">{{item.halAhole}}</span>
              <span v-if="item.resistance" style="padding-left: 8px;">{{item.resistance}}</span>
              <span v-if="item.pliesnumber" style="padding-left: 8px;">{{item.pliesnumber}}</span>
              <span v-if="item.specialCraftVal" style="padding-left: 8px;">{{item.specialCraftVal}}</span>
              <span v-if="item.throughHole" style="padding-left: 8px;">通孔：{{item.throughHole}}</span>
              <span v-if="item.countersinkHole" style="padding-left: 8px;">沉头孔：{{item.countersinkHole}}</span>
            </div>
            <div style="display: flex; justify-content: space-between;">
              <div>{{item.referenceType}}</div>
              <div style="color: #dc3545; font-weight: bold;">{{item.deliveryDate}}</div>
            </div>
            <div>备注：{{item.remark}}</div>
          </div>
        </template>
      </div>
    </div>
  </div>
</div>
